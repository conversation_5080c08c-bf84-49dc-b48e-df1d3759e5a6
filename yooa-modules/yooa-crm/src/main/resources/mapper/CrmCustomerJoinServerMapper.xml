<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmCustomerJoinServeMapper">

    <update id="clearExpireTime">
        UPDATE crm_customer_join_serve
        SET expire_time = NULL
    </update>

    <update id="correctionExpireTime">
        UPDATE crm_customer_join_serve cjs
            JOIN (SELECT id,
                         customer_id,
                         receive_time,
                         TIMESTAMPADD(SECOND, 0,
                                      LEAD(receive_time) OVER (PARTITION BY customer_id ORDER BY receive_time)) AS expire_time
                  FROM crm_customer_join_serve
                  WHERE status = 1) cjs_next ON cjs.id = cjs_next.id
        SET cjs.expire_time = cjs_next.expire_time
    </update>

    <select id="getList" resultType="com.yooa.crm.api.domain.vo.CustomerJoinServeVo">
        SELECT
        cjs.*,
        CASE
        WHEN c.create_time = fmt.min_create_time THEN '0'
        WHEN cf.id IS NULL THEN
        '0'
        ELSE '1'
        END AS accountType,
        <choose>
            <when test="query.type == 1">
                combined.dept_id AS dept_id,
                combined.user_id AS user_id,
            </when>
            <otherwise>
                op.dept_id AS dept_id,
                op.user_id AS user_id,
            </otherwise>
        </choose>
        ex.dept_name extendDeptName,
        ex.nick_name AS extendUserName,
        ex.dept_id AS extendDeptId,
        ex.ancestors_names AS extendAncestors,
        ex.ancestors AS extendAncestorsId,
        op.nick_name AS serveUserName,
        op.ancestors_names AS serveAncestors,
        op.ancestors AS serveAncestorsId,
        op.dept_id AS serveDeptId,
        (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS channelName,
        u2.nick_name AS channelNickName,
        f.language,
        f.fans_type AS fansType,
        f.friend_id AS friendId,
        f.extend_id AS extendId,
        f.friend_name AS friendName,
        CASE
        WHEN cf.id IS NULL THEN
        '0' ELSE '1'
        END bindType
        FROM
        (
        SELECT
        cjs.id,
        cjs.extend_id,
        cjs.serve_id,
        cjs.customer_id,
        cjs.receive_time
        FROM
        crm_customer_join_serve cjs
        where cjs.status = 1
        ) cjs
        LEFT JOIN crm_customer AS c ON cjs.customer_id = c.customer_id
        LEFT JOIN crm_customer_friend AS cf ON cf.customer_id = c.customer_id AND cf.py_extend_id = cjs.extend_id
        AND cf.begin_time &lt; cjs.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cjs.receive_time)
        LEFT JOIN (
        SELECT
        f.friend_id,
        f.friend_name,
        f.fans_type,
        f.extend_id,
        f.LANGUAGE,
        f.main_channel_id,
        f.pitcher_id,
        up.py_extend_ids
        FROM
        crm_friend AS f
        LEFT JOIN (
        SELECT
        GROUP_CONCAT(pd_user_id) AS py_extend_ids,
        user_id
        FROM yooa_system.sys_user_pd
        GROUP BY user_id
        ) up ON up.user_id = f.extend_id
        WHERE
        f.extend_id IS NOT NULL
        ) AS f ON f.friend_id = cf.friend_id
        AND FIND_IN_SET(cjs.extend_id, f.py_extend_ids)
        LEFT JOIN (
        SELECT
        cf.friend_id,
        MIN(cc.create_time) AS min_create_time
        FROM
        crm_customer_friend cf
        LEFT JOIN crm_customer cc ON cf.customer_id = cc.customer_id
        GROUP BY
        cf.friend_id
        ) fmt ON cf.friend_id = fmt.friend_id
        LEFT JOIN (
        SELECT
        up.pd_user_id,
        u.nick_name,
        d.dept_id,
        d.ancestors_names,
        d.ancestors,
        d.dept_name,
        u.user_id
        FROM yooa_system.sys_user_pd up
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        ) ex ON cjs.extend_id = ex.pd_user_id
        LEFT JOIN (
        SELECT
        up.pd_user_id,
        u.nick_name,
        d.dept_id,
        d.ancestors_names,
        d.ancestors,
        u.user_id
        FROM yooa_system.sys_user_pd up
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        ) op ON cjs.serve_id = op.pd_user_id
        LEFT JOIN (
        SELECT
        up.pd_user_id,
        d.dept_id,
        u.user_id
        FROM
        yooa_system.sys_user_pd up
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        ) combined ON
        combined.pd_user_id =
        CASE
        WHEN #{query.type} = 1 THEN cjs.serve_id
        ELSE cjs.extend_id
        END
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        WHERE
        1 = 1
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND cjs.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null and query.moduleType == null">
             <choose>
                 <when test="query.type == 0">
                     AND (FIND_IN_SET(#{query.deptId},ex.ancestors) OR ex.dept_id = #{query.deptId})
                 </when>
                 <otherwise>
                     AND (FIND_IN_SET(#{query.deptId},op.ancestors) OR op.dept_id = #{query.deptId})
                 </otherwise>
             </choose>
        </if>
        <if test="query.deptId != null and query.moduleType == 'pitcher'">
            LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
        </if>

        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery !=''">
            AND (f.friend_name like CONCAT('%',#{query.customerNameOrIdQuery},'%')
            OR cjs.customer_id = #{query.customerNameOrIdQuery}
            OR ex.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR op.nick_name LIKE CONCAT ('%', #{query.customerNameOrIdQuery},'%'))
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        GROUP BY cjs.id
        <if test="query.accountType != null">
            HAVING accountType = #{query.accountType}
        </if>
        ORDER BY cjs.receive_time DESC
    </select>

    <!-- 运营 - 二交列表 -->
    <select id="getListForOperate" resultType="com.yooa.crm.api.domain.vo.CustomerJoinServeVo">
        SELECT
        cjs.*,
        CASE
        WHEN c.create_time = fmt.min_create_time THEN '0'
        WHEN cf.id IS NULL THEN
        '0'
        ELSE '1'
        END AS accountType,
        <choose>
            <when test="query.type == 1">
                combined.dept_id AS dept_id,
                combined.user_id AS user_id,
            </when>
            <otherwise>
                opu.dept_id AS dept_id,
                opu.user_id AS user_id,
            </otherwise>
        </choose>
        ex.dept_name extendDeptName,
        ex.nick_name AS extendUserName,
        ex.dept_id AS extendDeptId,
        ex.ancestors_names AS extendAncestors,
        ex.ancestors AS extendAncestorsId,
        op.nick_name AS serveUserName,
        op.ancestors_names AS serveAncestors,
        op.ancestors AS serveAncestorsId,
        op.dept_id AS serveDeptId,
        (SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id) AS channelName,
        u2.nick_name AS channelNickName,
        f.language,
        f.fans_type AS fansType,
        f.friend_id AS friendId,
        f.extend_id AS extendId,
        f.friend_name AS friendName,
        CASE
        WHEN cf.id IS NULL THEN
        '0' ELSE '1'
        END bindType
        FROM
        (
        SELECT
        cjs.id,
        cjs.extend_id,
        cjs.serve_id,
        cjs.customer_id,
        cjs.receive_time
        FROM
        crm_customer_join_serve cjs
        INNER JOIN crm_customer_join_anchor cja ON cjs.customer_id = cja.customer_id
        INNER JOIN yooa_system.sys_user_pd sup ON cja.extend_id = sup.pd_user_id
        INNER JOIN yooa_system.sys_user opu ON sup.user_id = opu.user_id
        INNER JOIN yooa_system.sys_dept opd ON opu.dept_id = opd.dept_id
        where cjs.status = 1 AND cja.status = '1'
        ) cjs
        LEFT JOIN crm_customer AS c ON cjs.customer_id = c.customer_id
        LEFT JOIN crm_customer_friend AS cf ON cf.customer_id = c.customer_id AND cf.py_extend_id = cjs.extend_id
        AND cf.begin_time &lt; cjs.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cjs.receive_time)
        LEFT JOIN (
        SELECT
        f.friend_id,
        f.friend_name,
        f.language,
        f.fans_type,
        f.extend_id,
        f.pitcher_id,
        f.main_channel_id,
        MIN(c.create_time) AS min_create_time
        FROM
        crm_friend f
        LEFT JOIN crm_customer c ON f.friend_id = c.friend_id
        GROUP BY
        f.friend_id
        ) fmt ON c.friend_id = fmt.friend_id
        LEFT JOIN crm_friend f ON c.friend_id = f.friend_id
        LEFT JOIN (
        SELECT
        u.user_id,
        u.nick_name,
        d.dept_id,
        d.dept_name,
        d.ancestors_names,
        d.ancestors
        FROM
        yooa_system.sys_user u
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        ) ex ON cjs.extend_id = ex.user_id
        LEFT JOIN (
        SELECT
        u.user_id,
        u.nick_name,
        d.dept_id,
        d.dept_name,
        d.ancestors_names,
        d.ancestors
        FROM
        yooa_system.sys_user u
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        ) op ON cjs.serve_id = op.user_id
        LEFT JOIN (
        SELECT
        CASE
        WHEN #{query.type} = 1 THEN op.user_id
        ELSE ex.user_id
        END AS user_id,
        CASE
        WHEN #{query.type} = 1 THEN op.dept_id
        ELSE ex.dept_id
        END AS dept_id
        FROM
        (
        SELECT
        u.user_id,
        d.dept_id
        FROM
        yooa_system.sys_user u
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        ) ex
        CROSS JOIN (
        SELECT
        u.user_id,
        d.dept_id
        FROM
        yooa_system.sys_user u
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        ) op
        ) combined ON 1 = 1
        LEFT JOIN yooa_system.sys_user u1 ON f.pitcher_id = u1.user_id
        LEFT JOIN LATERAL (
        SELECT
        CASE
        WHEN f.pitcher_id IS NOT NULL THEN u1.nick_name
        ELSE NULL
        END AS nick_name
        ) u2 ON 1 = 1
        WHERE
        1 = 1
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND cjs.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null and query.moduleType == null">
             <choose>
                 <when test="query.type == 0">
                     AND (FIND_IN_SET(#{query.deptId},ex.ancestors) OR ex.dept_id = #{query.deptId})
                 </when>
                 <otherwise>
                     AND (FIND_IN_SET(#{query.deptId},op.ancestors) OR op.dept_id = #{query.deptId})
                 </otherwise>
             </choose>
        </if>
        <if test="query.deptId != null and query.moduleType == 'pitcher'">
            LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
        </if>

        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery !=''">
            AND (f.friend_name like CONCAT('%',#{query.customerNameOrIdQuery},'%')
            OR cjs.customer_id = #{query.customerNameOrIdQuery}
            OR ex.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR op.nick_name LIKE CONCAT ('%', #{query.customerNameOrIdQuery},'%'))
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        GROUP BY cjs.id
        <if test="query.accountType != null">
            HAVING accountType = #{query.accountType}
        </if>
        ORDER BY cjs.receive_time DESC
    </select>
</mapper>
