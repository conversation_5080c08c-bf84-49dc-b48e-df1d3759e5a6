package com.yooa.crm.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomerJoinServe;
import com.yooa.crm.api.domain.query.CustomerJoinServeQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinServeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户客服交接 - 数据层
 */
public interface CrmCustomerJoinServeMapper extends BaseMapper<CrmCustomerJoinServe> {

    int clearExpireTime();

    int correctionExpireTime();

    List<CustomerJoinServeVo> getList(Page page, @Param("query") CustomerJoinServeQuery query);

    /**
     * 运营 - 二交列表
     */
    List<CustomerJoinServeVo> getListForOperate(Page page, @Param("query") CustomerJoinServeQuery query);

    /**
     * 运营 - 投放二交列表
     */
    List<CustomerJoinServeVo> pitcherListForOperate(Page page, @Param("query") CustomerJoinServeQuery query);
}




